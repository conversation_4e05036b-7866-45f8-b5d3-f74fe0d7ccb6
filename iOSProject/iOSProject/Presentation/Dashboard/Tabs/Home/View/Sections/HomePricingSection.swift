//
//  HomePricingSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomePricingSection: View {
    @Environment(\.appColors) var colors
    let pricingPlans: [PricingPlan]
    let onGetStarted: () -> Void
    
    var body: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 16.h) {
                Text(AppConstants.Strings.pricingTitle)
                    .display30SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Text(AppConstants.Strings.pricingDescription)
                    .headline18Regular()
                    .foregroundColor(colors.secondaryText)
                    .lineSpacing(6)
            }
            .padding(.horizontal, 16.h)
            
            // Pricing Cards
            VStack(spacing: 16.h) {
                ForEach(pricingPlans, id: \.id) { plan in
                    PricingCardView(plan: plan, onGetStarted: onGetStarted)
                }
            }
            .padding(.horizontal, 16.h)
        }
        .padding(.vertical, 48.h)
    }
}

#Preview {
    HomePricingSection(pricingPlans: PricingPlan.mockPlans, onGetStarted: {})
        .attachAllEnvironmentObjects()
}
