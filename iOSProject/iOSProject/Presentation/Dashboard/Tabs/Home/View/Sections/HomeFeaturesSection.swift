//
//  HomeFeaturesSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeFeaturesSection: View {
    @Environment(\.appColors) var colors
    let features: [FeatureItem]
    
    var body: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 16.h) {
                Text(AppConstants.Strings.featuresTitle)
                    .display30SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Text(AppConstants.Strings.featuresDescription)
                    .headline18Regular()
                    .foregroundColor(colors.secondaryText)
                    .lineSpacing(6)
            }
            .padding(.horizontal, 16.h)
            
            // Features Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16.h) {
                ForEach(features, id: \.id) { feature in
                    FeatureItemView(feature: feature)
                }
            }
            .padding(.horizontal, 16.h)
        }
        .padding(.vertical, 48.h)
    }
}

#Preview {
    HomeFeaturesSection(features: FeatureItem.mockFeatures)
        .attachAllEnvironmentObjects()
}
