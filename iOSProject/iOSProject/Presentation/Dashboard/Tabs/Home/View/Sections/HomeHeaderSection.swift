//
//  HomeHeaderSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeHeaderSection: View {
    @Environment(\.appColors) var colors
    
    var body: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 60.h)
            
            HStack(spacing: 8.h) {
                // New Feature Badge
                HStack(spacing: 8.h) {
                    Text(AppConstants.Strings.newFeatureBadge)
                        .body12Medium()
                        .foregroundColor(colors.badgeText)
                        .padding(.horizontal, 12.h)
                        .padding(.vertical, 4.h)
                        .background(colors.surface)
                        .clipShape(RoundedRectangle(cornerRadius: 11.h))

                    Text(AppConstants.Strings.personalizedCoaching)
                        .body12Medium()
                        .foregroundColor(colors.primaryText)
                }
                
                Spacer()
                
                Image(systemName: AppConstants.SystemIcons.arrowRight)
                    .font(.system(size: 16))
                    .foregroundColor(colors.arrowIcon)
            }
            .padding(.horizontal, 16.h)
            
            Spacer().frame(height: 32.h)
        }
    }
}

#Preview {
    HomeHeaderSection()
        .attachAllEnvironmentObjects()
}
