//
//  HomePartnersSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomePartnersSection: View {
    @Environment(\.appColors) var colors
    
    var body: some View {
        VStack(spacing: 24.h) {
            Text(AppConstants.Strings.trustedByInvestors)
                .body14Medium()
                .foregroundColor(colors.secondaryText)
            
            // Partner Logos
            HStack(spacing: 32.h) {
                ForEach(AppConstants.PartnerLogos.allCases, id: \.self) { partner in
                    PartnerLogoView(partner: PartnerLogo(logoName: partner.rawValue, companyName: partner.companyName))
                }
            }
            .padding(.horizontal, 16.h)
        }
        .padding(.vertical, 32.h)
        .background(colors.surface)
    }
}

#Preview {
    HomePartnersSection()
        .attachAllEnvironmentObjects()
}
