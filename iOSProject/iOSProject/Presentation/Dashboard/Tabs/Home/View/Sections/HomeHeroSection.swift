//
//  HomeHeroSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeHeroSection: View {
    @Environment(\.appColors) var colors
    let onGetStarted: () -> Void
    
    var body: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 16.h) {
                Text(AppConstants.Strings.portfolioPerformanceTitle)
                    .display36SemiBold()
                    .foregroundColor(colors.primaryText)
                    .lineSpacing(4)

                Text(AppConstants.Strings.portfolioPerformanceSubtitle)
                    .headline18Regular()
                    .foregroundColor(colors.secondaryText)
                    .lineSpacing(6)
            }
            .padding(.horizontal, 16.h)
            
            // Hero Image
            Image(ImageConstants.imgHeroImage)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxWidth: .infinity)
                .frame(height: 240.h)
                .accessibilityLabel(AppConstants.Strings.heroImage)
            
            // Get Started Button
            CustomButton(
                title: AppConstants.Strings.startFreeTrial,
                variant: .filled,
                action: onGetStarted
            )
            .padding(.horizontal, 16.h)
        }
        .padding(.vertical, 32.h)
    }
}

#Preview {
    HomeHeroSection(onGetStarted: {})
        .attachAllEnvironmentObjects()
}
