//
//  HomeCTASection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeCTASection: View {
    @Environment(\.appColors) var colors
    let onAppStoreButtonTapped: () -> Void
    let onPlayStoreButtonTapped: () -> Void

    var body: some View {
        VStack(spacing: 32.h) {
            VStack(spacing: 16.h) {
                Text(AppConstants.Strings.startFreeTrial)
                    .display30SemiBold()
                    .foregroundColor(colors.ctaText)
                    .lineSpacing(4)

                Text(AppConstants.Strings.personalPerformanceTracking)
                    .headline18Regular()
                    .foregroundColor(colors.ctaSubtext)
                    .lineSpacing(6)
            }

            // CTA Buttons
            HStack(spacing: 16.h) {
                AppStoreButtonView(type: .appStore) {
                    onAppStoreButtonTapped()
                }

                AppStoreButtonView(type: .playStore) {
                    onPlayStoreButtonTapped()
                }
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 64.h)
        .background(colors.ctaBackground)
    }
}

#Preview {
    HomeCTASection(
        onAppStoreButtonTapped: {},
        onPlayStoreButtonTapped: {}
    )
}
