import SwiftUI

// MARK: - Pricing Card View
struct PricingCardView: View {
    let plan: PricingPlan
    let onGetStarted: () -> Void
    @Environment(\.appColors) var colors
    
    var body: some View {
        ZStack(alignment: .topTrailing) {
            // Main Card
            VStack(spacing: 24.h) {
                // Popular badge spacing
                if plan.isPopular {
                    Spacer().frame(height: 20.h)
                }
                
                // Price
                Text(plan.price)
                    .display36SemiBold()
                    .themedTextColor(.primary)
                
                // Title and Description
                VStack(spacing: 4.h) {
                    Text(plan.title)
                        .headline20SemiBold()
                        .themedTextColor(.primary)
                    
                    Text(plan.description)
                        .title16Regular()
                        .themedTextColor(.secondary)
                }
                
                // Features List
                VStack(spacing: 16.h) {
                    ForEach(plan.features, id: \.self) { feature in
                        featureRow(feature)
                    }
                }
                
                // Get Started Button
                CustomButton(
                    title: "Get started",
                    action: onGetStarted
                )
            }
            .padding(24.h)
            .background(colors.surface)
            .overlay(
                RoundedRectangle(cornerRadius: 16.h)
                    .stroke(colors.borderDefault, lineWidth: 1)
            )
            .clipShape(RoundedRectangle(cornerRadius: 16.h))
            .shadow(
                color: colors.shadowColor,
                radius: 8,
                x: 0,
                y: 4
            )
            
            // Popular Badge
            if plan.isPopular {
                popularBadge
                    .offset(y: -32.h)
            }
        }
    }
    
    // MARK: - Feature Row
    private func featureRow(_ feature: String) -> some View {
        HStack(spacing: 12.h) {
            // Check Icon
            ZStack {
                Circle()
                    .fill(Color(hex: "D0FADF"))
                    .frame(width: 24.h, height: 24.h)
                
                Image(systemName: "checkmark")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(Color(hex: "16A34A"))
            }
            
            // Feature Text
            Text(feature)
                .title16Regular()
                .themedTextColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
    }
    
    // MARK: - Popular Badge
    private var popularBadge: some View {
        HStack(spacing: 8.h) {
            // Star Icon
            Image(systemName: "star.fill")
                .font(.system(size: 16))
                .foregroundColor(colors.brandPrimary)
            
            Text("Most popular!")
                .body14Medium()
                .foregroundColor(colors.brandPrimary)
        }
        .padding(.horizontal, 12.h)
        .padding(.vertical, 6.h)
        .background(
            Capsule()
                .fill(Color(hex: "F9F5FF"))
                .overlay(
                    Capsule()
                        .stroke(Color(hex: "E9D7FE"), lineWidth: 1)
                )
        )
    }
}

// MARK: - Preview
struct PricingCardView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 32) {
            PricingCardView(
                plan: PricingPlan(
                    price: "$10/mth",
                    title: "Basic plan",
                    description: "Billed annually.",
                    isPopular: true,
                    features: [
                        "Access to all basic features",
                        "Basic reporting and analytics",
                        "Up to 10 individual users"
                    ]
                ),
                onGetStarted: { }
            )
            
            PricingCardView(
                plan: PricingPlan(
                    price: "$20/mth",
                    title: "Business plan",
                    description: "Billed annually.",
                    isPopular: false,
                    features: [
                        "200+ integrations",
                        "Advanced reporting",
                        "Up to 20 individual users"
                    ]
                ),
                onGetStarted: { }
            )
        }
        .padding()
        .attachAllEnvironmentObjects()
    }
}
