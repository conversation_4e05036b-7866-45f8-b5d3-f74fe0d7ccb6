import SwiftUI


extension HomeView {
    static func build(container: DependencyContainer) -> some View {
        HomeView(viewModel: HomeViewModel(newsUseCase: container.newsUseCase))
           
    }
}

// MARK: - Landing View

struct HomeView: View {
    @StateObject private var viewModel: HomeViewModel
    @Environment(\.appColors) var colors
    
    init(viewModel: HomeViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        MainScrollBody {
            LazyVStack(spacing: 0) {
                // Header Section
                HomeHeaderSection()

                // Hero Section
                HomeHeroSection(onGetStarted: { viewModel.getStartedTapped() })

                // Partners Section
                HomePartnersSection()

                // Features Section
                HomeFeaturesSection(features: viewModel.landingData.features)

                // CTA Section
                HomeCTASection(
                    onAppStoreButtonTapped: { viewModel.appStoreButtonTapped() },
                    onPlayStoreButtonTapped: { viewModel.playStoreButtonTapped() }
                )

                // Divider
                dividerSection

                // Pricing Section
                HomePricingSection(pricingPlans: viewModel.landingData.pricingPlans, onGetStarted: viewModel.getStartedTapped)

                // Divider
                dividerSection

                // Newsletter Section
                HomeNewsletterSection(
                    email: $viewModel.newsletterEmail,
                    isSubscribing: viewModel.isSubscribing,
                    onSubscribe: { viewModel.subscribeToNewsletter() }
                )
            }
        }
        .background(colors.background)
        .refreshable {
            viewModel.refreshData()
        }
        .alert(AppConstants.Strings.error, isPresented: $viewModel.showError) {
            Button(AppConstants.Strings.ok) {}
        } message: {
            Text(viewModel.errorMessage)
        }
    }

    // MARK: - Divider Section

    private var dividerSection: some View {
        Rectangle()
            .fill(colors.borderDefault)
            .frame(height: 1)
            .padding(.horizontal, 16.h)
    }



}



// MARK: - Preview
#Preview {
    NavigationStack{
        HomeView.build(container: DependencyContainer())
            .provideTheme(dark: false)
            .attachAllEnvironmentObjects()
    }
}
