import SwiftUI

// MARK: - Profile View

struct ProfileView: View {
    @StateObject private var viewModel: ProfileViewModel
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.appColors) var colors
    @Namespace private var animate
    let isDrawer: Bool
    @Binding private var isDrawerOpen: Bool

    init(viewModel: ProfileViewModel, isDrawer: Bool = false, isDrawerOpen: Binding<Bool>) {
        self._viewModel = StateObject(wrappedValue: viewModel)
        self.isDrawer = isDrawer
        self._isDrawerOpen = isDrawerOpen
    }

    static func build(container: DependencyContainer, isDrawer: Bool = false, isDrawerOpen: Binding<Bool>) -> ProfileView {
        let viewModel = ProfileViewModel(navigationCoordinator: container.navigationCoordinator)
        return ProfileView(viewModel: viewModel, isDrawer: isDrawer, isDrawerOpen: isDrawerOpen)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
//            if !isDrawer {
//                headerSection
//            }
            headerSection
            ScrollView {
                VStack(alignment: .leading, spacing: 0) {
                    profileSection
                    menuItemsSection
                }
            }
        }
        .background(colors.surface)
        .safeAreaPadding(.top)
        .cornerRadius(isDrawer ? 16 : 0, corners: isDrawer ? [.topLeft, .bottomLeft] : [])
    }
    
    // MARK: - Header Section

    private var headerSection: some View {
        HStack {
            Button(action: {
                // Handle back navigation
                self.isDrawerOpen.toggle()
            }) {
                Image(ImageConstants.arrowLeftIcon)
                    .resizable()
                    .frame(width: 24.h, height: 24.h)
                    .foregroundColor(colors.onSurface)
                    .rotateBasedOnLanguage()
            }
            
            Spacer()
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 12.h)
        .background(colors.surface)
    }
    
    // MARK: - Profile Section

    private var profileSection: some View {
        VStack(spacing: 16.h) {
            // Profile Image
            Button(action: {
                // Handle profile image edit
            }) {
                ZStack {
                    Image(ImageConstants.defaultProfileAvatar)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 80.h, height: 80.h)
                        .clipShape(Circle())
                    
                    // Edit overlay
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(ImageConstants.imgEditImage)
                                .resizable()
                                .frame(width: 24.h, height: 24.h)
                                .background(colors.overlayBackground)
                                .clipShape(Circle())
                        }
                    }
                    .frame(width: 80.h, height: 80.h)
                }
            }
            
            // User Info
            VStack(spacing: 4.h) {
                Text(viewModel.user.name)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(colors.onSurface)

                Text(viewModel.user.email)
                    .body14Regular()
                    .foregroundColor(colors.onSurfaceVariant)
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 24.h)
    }
    
    // MARK: - Menu Items Section

    private var menuItemsSection: some View {
        VStack(spacing: 0) {
            darkModeToggle
            divider
            languageSwitch
            divider
            
            ForEach(menuItems, id: \.title) { item in
                menuItem(item)
                if item.title != "Logout" {
                    divider
                }
            }
        }
        .padding(.horizontal, 16.h)
    }
    
    // MARK: - Dark Mode Toggle

    private var darkModeToggle: some View {
        HStack {
            Image(ImageConstants.darkMode)
                .renderingMode(.template)
                .resizable()
                .frame(width: 20.h, height: 20.h)
                .foregroundColor(colors.onSurface)
            
            Text("Dark Mode")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(colors.onSurface.opacity(0.75))
            
            Spacer()
            
            Toggle("", isOn: $themeManager.isDarkMode)
                .toggleStyle(CustomToggleStyle())
                .frame(width: 44.h, height: 24.h)
        }
        .padding(.vertical, 12.h)
    }
    
    // MARK: - Language Switch

    private var languageSwitch: some View {
        HStack {
            Image(ImageConstants.imgSettings)
                .renderingMode(.template)
                .resizable()
                .frame(width: 20.h, height: 20.h)
                .foregroundColor(colors.onSurface)
            
            Text("Language")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(colors.onSurface.opacity(0.75))
            
            Spacer()
            
            languageToggle
        }
        .padding(.vertical, 12.h)
    }
    
    // MARK: - Language Toggle

    private var languageToggle: some View {
        HStack(spacing: 0) {
            languageOption(label: "EN", isSelected: viewModel.isEnglish, isFirst: true) { viewModel.changeLanguage(to: "en") }
            
            languageOption(label: "عر", isSelected: !viewModel.isEnglish, isFirst: false) { viewModel.changeLanguage(to: "ar") }
        }
        .background(colors.languageToggleBackground)
        .cornerRadius(12.h)
        .overlay(
            RoundedRectangle(cornerRadius: 12.h)
                .stroke(colors.languageToggleBorder, lineWidth: 1)
        )
        .animation(.bouncy, value: viewModel.isEnglish)
    }
    
    private func languageOption(label: String, isSelected: Bool, isFirst: Bool, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            Text(label)
                .body12SemiBold()
                .foregroundColor(isSelected ? colors.toggleKnob : colors.onSurface.opacity(0.75))
                .frame(width: 32.h, height: 24.h)
                .background {
                    if isSelected {
                        colors.toggleActiveBackground
                            .cornerRadius(8.h)
                            .matchedGeometryEffect(id: "language.toggle", in: animate)
                    } else {
                        Color.clear
                    }
                }
               
                
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Menu Item

    private func menuItem(_ item: ProfileMenuItem) -> some View {
        Button(action: item.action) {
            HStack {
                Image(item.icon)
                    .renderingMode(.template)
                    .resizable()
                    .frame(width: 20.h, height: 20.h)
                    .foregroundColor(item.isLogout ? colors.error : colors.onSurface)
                    
                Text(item.title.localize)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor((item.isLogout ? colors.error : colors.onSurface).opacity(0.75))

                Spacer()
            }
            .padding(.vertical, 12.h)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Divider

    private var divider: some View {
        Rectangle()
            .fill(colors.dividerColor)
            .frame(height: 1)
    }
    
    // MARK: - Menu Items Data

    private var menuItems: [ProfileMenuItem] {
        viewModel.createMenuItems()
    }
}

// MARK: - Menu Item Model (moved to ProfileViewModel.swift as ProfileMenuItem)

// MARK: - Custom Toggle Style

struct CustomToggleStyle: ToggleStyle {
    @Environment(\.appColors) var colors

    func makeBody(configuration: Configuration) -> some View {
        HStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(configuration.isOn ? colors.toggleActiveBackground : colors.toggleInactiveBackground)
                .frame(width: 44, height: 24)
                .overlay(
                    Circle()
                        .fill(colors.toggleKnob)
                        .frame(width: 20, height: 20)
                        .offset(x: configuration.isOn ? 10 : -10)
                        .animation(.easeInOut(duration: 0.2), value: configuration.isOn)
                )
                .onTapGesture {
                    configuration.isOn.toggle()
                }
        }
    }
}

// MARK: - Corner Radius Extension

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

// MARK: - Preview

struct ProfileView_Previews: PreviewProvider {
    static var previews: some View {
        ProfileView.build(container: DependencyContainer(), isDrawer: true, isDrawerOpen: .constant(true))
            .environmentObject(ThemeManager())
    }
}
