import SwiftUI

@MainActor
final class ThemeManager: ObservableObject {
    @AppStorage(.isDarkMode) var isDarkMode: Bool = false

    // Derive desired scheme: always lock to isDarkMode
    var preferredScheme: ColorScheme? {
        isDarkMode ? .dark : .light
    }

    func toggle() { isDarkMode.toggle() }
    func set(_ dark: Bool) { isDarkMode = dark }
}

struct ThemeProvider: ViewModifier {
    @EnvironmentObject private var theme: ThemeManager

    // Choose palette based on isDarkMode only
    private var colors: AppColorsProtocol {
        theme.isDarkMode ? AppDarkColors() : AppLightColors()
    }

    func body(content: Content) -> some View {
        content
            .tint(colors.appMain)
            .environment(\.appColors, colors)
            .preferredColorScheme(theme.preferredScheme) // always .dark or .light
    }
}

extension View {
    func provideTheme() -> some View { modifier(ThemeProvider()) }
}


// MARK: - Theme Environment Key for Colors

struct AppColorsEnvironmentKey: EnvironmentKey {
    static let defaultValue: AppColorsProtocol = AppLightColors()
}

extension EnvironmentValues {
    var appColors: AppColorsProtocol {
        get { self[AppColorsEnvironmentKey.self] }
        set { self[AppColorsEnvironmentKey.self] = newValue }
    }
}


//
// MARK: - Theme-aware Text Color

struct ThemedTextColor: ViewModifier {
    @Environment(\.appColors) var colors
    let colorType: TextColorType

    enum TextColorType {
        case primary
        case brand
        case secondary
        case onSurface
        case onBackground
        case error
    }

    func body(content: Content) -> some View {
        content
            .foregroundStyle(textColor)
    }

    private var textColor: Color {
        switch colorType {
        case .primary:
            return colors.primaryText
        case .brand:
            return colors.brandPrimary
        case .secondary:
            return colors.secondaryText
        case .onSurface:
            return colors.onSurface
        case .onBackground:
            return colors.onBackground
        case .error:
            return colors.error
        }
    }
}

extension View {
    func themedTextColor(_ type: ThemedTextColor.TextColorType = .primary) -> some View {
        modifier(ThemedTextColor(colorType: type))
    }
}
