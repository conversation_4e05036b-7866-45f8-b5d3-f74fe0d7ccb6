//
//  ThemeManagerTests.swift
//  iOSProjectTests
//
//  Created by Apple on 10/08/2025.
//

import XCTest
@testable import iOSProject

final class DarkModeThemeTests: XCTestCase {
    
    var themeManager: ThemeManager!
    
    override func setUpWithError() throws {
        themeManager = ThemeManager()
    }
    
    override func tearDownWithError() throws {
        themeManager = nil
    }
    
    func testThemeToggling() throws {
        // Test initial state
        let initialDarkMode = themeManager.isDarkMode
        
        // Toggle theme
        themeManager.toggleTheme()
        
        // Verify theme changed
        XCTAssertNotEqual(themeManager.isDarkMode, initialDarkMode, "Theme should toggle")
        
        // Toggle back
        themeManager.toggleTheme()
        
        // Verify theme returned to original state
        XCTAssertEqual(themeManager.isDarkMode, initialDarkMode, "Theme should return to original state")
    }
    
    func testDarkModeColors() throws {
        // Set to dark mode
        themeManager.setTheme(true)
        
        // Verify dark colors are applied
        XCTAssertTrue(themeManager.isDarkMode, "Should be in dark mode")
        XCTAssertTrue(themeManager.currentColors is AppDarkColors, "Should use dark colors")
        
        // Test specific dark color values
        let darkColors = themeManager.currentColors
        XCTAssertEqual(darkColors.background.description, DarkThemeColors.background.description, "Background should be dark")
        XCTAssertEqual(darkColors.onBackground.description, DarkThemeColors.onBackground.description, "Text should be light")
    }
    
    func testLightModeColors() throws {
        // Set to light mode
        themeManager.setTheme(false)
        
        // Verify light colors are applied
        XCTAssertFalse(themeManager.isDarkMode, "Should be in light mode")
        XCTAssertTrue(themeManager.currentColors is AppLightColors, "Should use light colors")
        
        // Test specific light color values
        let lightColors = themeManager.currentColors
        XCTAssertEqual(lightColors.background.description, LightThemeColors.background.description, "Background should be light")
        XCTAssertEqual(lightColors.onBackground.description, LightThemeColors.onBackground.description, "Text should be dark")
    }
    
    func testThemePersistence() throws {
        // Set to dark mode
        themeManager.setTheme(true)
        
        // Create new theme manager instance (simulates app restart)
        let newThemeManager = ThemeManager()
        
        // Verify theme persisted
        XCTAssertTrue(newThemeManager.isDarkMode, "Dark mode should persist across app restarts")
        
        // Set to light mode
        themeManager.setTheme(false)
        
        // Create another new instance
        let anotherThemeManager = ThemeManager()
        
        // Verify light mode persisted
        XCTAssertFalse(anotherThemeManager.isDarkMode, "Light mode should persist across app restarts")
    }
    
    func testColorContrast() throws {
        // Test dark mode contrast
        themeManager.setTheme(true)
        let darkColors = themeManager.currentColors
        
        // Verify dark background with light text (basic contrast check)
        XCTAssertNotEqual(darkColors.background.description, darkColors.onBackground.description, 
                         "Background and text colors should be different for contrast")
        
        // Test light mode contrast
        themeManager.setTheme(false)
        let lightColors = themeManager.currentColors
        
        // Verify light background with dark text (basic contrast check)
        XCTAssertNotEqual(lightColors.background.description, lightColors.onBackground.description,
                         "Background and text colors should be different for contrast")
    }
    
    func testFollowSystemAppearance() throws {
        // Test setting follow system appearance
        themeManager.setFollowSystemAppearance(true)
        XCTAssertTrue(themeManager.followSystemAppearance, "Should follow system appearance")
        
        themeManager.setFollowSystemAppearance(false)
        XCTAssertFalse(themeManager.followSystemAppearance, "Should not follow system appearance")
    }
}
