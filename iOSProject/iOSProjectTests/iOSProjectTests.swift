//
//  iOSProjectTests.swift
//  iOSProjectTests
//
//  Created by Apple on 08/08/2025.
//

import Testing
import SwiftUI
import Foundation
@testable import iOSProject

// MARK: - Domain Layer Tests

// MARK: - User Model Tests
struct UserModelTests {

    @Test func testUserInitialization() async throws {
        // Test default initialization
        let user = User(email: "<EMAIL>", name: "Test User")

        #expect(!user.id.isEmpty, "User ID should be generated")
        #expect(user.email == "<EMAIL>")
        #expect(user.name == "Test User")
        #expect(user.profileImageURL == nil)
        #expect(user.isEmailVerified == false)
        #expect(user.createdAt != nil)
        #expect(user.updatedAt != nil)
    }

    @Test func testUserInitializationWithAllParameters() async throws {
        let testDate = Date()
        let user = User(
            id: "custom_id",
            email: "<EMAIL>",
            name: "Custom User",
            profileImageURL: "https://example.com/image.jpg",
            isEmailVerified: true,
            createdAt: testDate,
            updatedAt: testDate
        )

        #expect(user.id == "custom_id")
        #expect(user.email == "<EMAIL>")
        #expect(user.name == "Custom User")
        #expect(user.profileImageURL == "https://example.com/image.jpg")
        #expect(user.isEmailVerified == true)
        #expect(user.createdAt == testDate)
        #expect(user.updatedAt == testDate)
    }

    @Test func testUserDisplayName() async throws {
        // Test with non-empty name
        let userWithName = User(email: "<EMAIL>", name: "John Doe")
        #expect(userWithName.displayName == "John Doe")

        // Test with empty name
        let userWithoutName = User(email: "<EMAIL>", name: "")
        #expect(userWithoutName.displayName == "<EMAIL>")

        // Test with whitespace-only name (current implementation doesn't trim)
        let userWithWhitespaceName = User(email: "<EMAIL>", name: "   ")
        #expect(userWithWhitespaceName.displayName == "   ", "Current implementation returns whitespace name as-is")
    }

    @Test func testUserInitials() async throws {
        // Test with full name
        let userFullName = User(email: "<EMAIL>", name: "John Doe")
        #expect(userFullName.initials == "JD")

        // Test with single name
        let userSingleName = User(email: "<EMAIL>", name: "John")
        #expect(userSingleName.initials == "J")

        // Test with empty name
        let userEmptyName = User(email: "<EMAIL>", name: "")
        #expect(userEmptyName.initials == "")

        // Test with multiple names
        let userMultipleNames = User(email: "<EMAIL>", name: "John Michael Doe")
        #expect(userMultipleNames.initials == "JD")

        // Test with lowercase names
        let userLowercase = User(email: "<EMAIL>", name: "john doe")
        #expect(userLowercase.initials == "JD")

        // Test with special characters
        let userSpecialChars = User(email: "<EMAIL>", name: "Jean-Pierre O'Connor")
        #expect(userSpecialChars.initials == "JO")
    }

    @Test func testUserEquality() async throws {
        let testDate = Date()

        let user1 = User(
            id: "same_id",
            email: "<EMAIL>",
            name: "Test User",
            isEmailVerified: true,
            createdAt: testDate,
            updatedAt: testDate
        )

        let user2 = User(
            id: "same_id",
            email: "<EMAIL>",
            name: "Test User",
            isEmailVerified: true,
            createdAt: testDate,
            updatedAt: testDate
        )

        let user3 = User(
            id: "different_id",
            email: "<EMAIL>",
            name: "Test User",
            isEmailVerified: true,
            createdAt: testDate,
            updatedAt: testDate
        )

        #expect(user1 == user2, "Users with same properties including dates should be equal")
        #expect(user1 != user3, "Users with different IDs should not be equal")
    }

    @Test func testUserMockData() async throws {
        let mockUser = User.mock

        #expect(mockUser.email == "<EMAIL>")
        #expect(mockUser.name == "John Doe")
        #expect(mockUser.profileImageURL == nil)
        #expect(mockUser.isEmailVerified == true)
        #expect(!mockUser.id.isEmpty)
        #expect(mockUser.displayName == "John Doe")
        #expect(mockUser.initials == "JD")
    }

    @Test func testUserCodable() async throws {
        let originalUser = User(
            id: "test_id",
            email: "<EMAIL>",
            name: "Test User",
            profileImageURL: "https://example.com/image.jpg",
            isEmailVerified: true
        )

        // Test encoding
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalUser)
        #expect(data.count > 0, "User should be encodable")

        // Test decoding
        let decoder = JSONDecoder()
        let decodedUser = try decoder.decode(User.self, from: data)

        #expect(decodedUser.id == originalUser.id)
        #expect(decodedUser.email == originalUser.email)
        #expect(decodedUser.name == originalUser.name)
        #expect(decodedUser.profileImageURL == originalUser.profileImageURL)
        #expect(decodedUser.isEmailVerified == originalUser.isEmailVerified)
    }

    @Test func testUserEdgeCases() async throws {
        // Test with very long name
        let longName = String(repeating: "a", count: 1000)
        let userLongName = User(email: "<EMAIL>", name: longName)
        #expect(userLongName.name == longName)
        #expect(userLongName.displayName == longName)

        // Test with special characters in name
        let specialName = "José María Aznar-López"
        let userSpecialName = User(email: "<EMAIL>", name: specialName)
        #expect(userSpecialName.name == specialName)
        #expect(userSpecialName.displayName == specialName)

        // Test with emoji in name
        let emojiName = "John 😀 Doe"
        let userEmojiName = User(email: "<EMAIL>", name: emojiName)
        #expect(userEmojiName.name == emojiName)

        // Test with very long email
        let longEmail = "<EMAIL>"
        let userLongEmail = User(email: longEmail, name: "Test User")
        #expect(userLongEmail.email == longEmail)
    }
}

// MARK: - Authentication State Tests
struct AuthenticationStateTests {

    @Test func testAuthenticationStateAuthenticated() async throws {
        let user = User.mock
        let state = AuthenticationState.authenticated(user)

        switch state {
        case .authenticated(let authenticatedUser):
            #expect(authenticatedUser == user)
        default:
            #expect(Bool(false), "State should be authenticated")
        }
    }

    @Test func testAuthenticationStateUnauthenticated() async throws {
        let state = AuthenticationState.unauthenticated

        switch state {
        case .unauthenticated:
            #expect(Bool(true), "State should be unauthenticated")
        default:
            #expect(Bool(false), "State should be unauthenticated")
        }
    }

    @Test func testAuthenticationStateLoading() async throws {
        let state = AuthenticationState.loading

        switch state {
        case .loading:
            #expect(Bool(true), "State should be loading")
        default:
            #expect(Bool(false), "State should be loading")
        }
    }
}

// MARK: - Login Credentials Tests
struct LoginCredentialsTests {

    @Test func testLoginCredentialsInitialization() async throws {
        let credentials = LoginCredentials(email: "<EMAIL>", password: "password123")

        #expect(credentials.email == "<EMAIL>")
        #expect(credentials.password == "password123")
    }

    @Test func testLoginCredentialsWithEmptyValues() async throws {
        let credentials = LoginCredentials(email: "", password: "")

        #expect(credentials.email == "")
        #expect(credentials.password == "")
    }

    @Test func testLoginCredentialsWithSpecialCharacters() async throws {
        let credentials = LoginCredentials(
            email: "<EMAIL>",
            password: "P@ssw0rd!#$"
        )

        #expect(credentials.email == "<EMAIL>")
        #expect(credentials.password == "P@ssw0rd!#$")
    }
}

// MARK: - Registration Data Tests
struct RegistrationDataTests {

    @Test func testRegistrationDataInitialization() async throws {
        let registrationData = RegistrationData(
            email: "<EMAIL>",
            password: "password123",
            name: "Test User",
            confirmPassword: "password123"
        )

        #expect(registrationData.email == "<EMAIL>")
        #expect(registrationData.password == "password123")
        #expect(registrationData.name == "Test User")
        #expect(registrationData.confirmPassword == "password123")
    }

    @Test func testRegistrationDataPasswordMismatch() async throws {
        let registrationData = RegistrationData(
            email: "<EMAIL>",
            password: "password123",
            name: "Test User",
            confirmPassword: "differentPassword"
        )

        #expect(registrationData.password != registrationData.confirmPassword)
    }

    @Test func testRegistrationDataWithEmptyValues() async throws {
        let registrationData = RegistrationData(
            email: "",
            password: "",
            name: "",
            confirmPassword: ""
        )

        #expect(registrationData.email == "")
        #expect(registrationData.password == "")
        #expect(registrationData.name == "")
        #expect(registrationData.confirmPassword == "")
    }
}

// MARK: - Color Protocol Tests
struct ColorProtocolTests {

    @Test func testLightColorsImplementation() async throws {
        let lightColors = AppLightColors()

        // Test that all required colors are implemented
        #expect(lightColors.primary != nil)
        #expect(lightColors.background != nil)
        #expect(lightColors.primaryText != nil)
        #expect(lightColors.dividerColor != nil)
        #expect(lightColors.ctaBackground != nil)
        #expect(lightColors.toastCloseButton != nil)
    }

    @Test func testDarkColorsImplementation() async throws {
        let darkColors = AppDarkColors()

        // Test that all required colors are implemented
        #expect(darkColors.primary != nil)
        #expect(darkColors.background != nil)
        #expect(darkColors.primaryText != nil)
        #expect(darkColors.dividerColor != nil)
        #expect(darkColors.ctaBackground != nil)
        #expect(darkColors.toastCloseButton != nil)
    }

    @Test func testColorDifferences() async throws {
        let lightColors = AppLightColors()
        let darkColors = AppDarkColors()

        // Test that light and dark themes have different colors for key elements
        #expect(lightColors.background != darkColors.background)
        #expect(lightColors.primaryText != darkColors.primaryText)
        #expect(lightColors.surface != darkColors.surface)
        #expect(lightColors.dividerColor != darkColors.dividerColor)
    }
}

// MARK: - NewsItem Model Tests
struct NewsItemModelTests {

    @Test func testNewsItemInitialization() async throws {
        let newsItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            category: .technology
        )

        #expect(!newsItem.id.isEmpty, "NewsItem ID should be generated")
        #expect(newsItem.title == "Test News")
        #expect(newsItem.description == "Test Description")
        #expect(newsItem.content == "Test Content")
        #expect(newsItem.author == "Test Author")
        #expect(newsItem.category == .technology)
        #expect(newsItem.imageURL == nil)
        #expect(newsItem.tags.isEmpty)
        #expect(newsItem.isBookmarked == false)
        #expect(newsItem.publishedAt != nil)
    }

    @Test func testNewsItemInitializationWithAllParameters() async throws {
        let testDate = Date()
        let newsItem = NewsItem(
            id: "custom_id",
            title: "Custom News",
            description: "Custom Description",
            content: "Custom Content",
            imageURL: "https://example.com/image.jpg",
            author: "Custom Author",
            publishedAt: testDate,
            category: .business,
            tags: ["tag1", "tag2"],
            isBookmarked: true
        )

        #expect(newsItem.id == "custom_id")
        #expect(newsItem.title == "Custom News")
        #expect(newsItem.description == "Custom Description")
        #expect(newsItem.content == "Custom Content")
        #expect(newsItem.imageURL == "https://example.com/image.jpg")
        #expect(newsItem.author == "Custom Author")
        #expect(newsItem.publishedAt == testDate)
        #expect(newsItem.category == .business)
        #expect(newsItem.tags == ["tag1", "tag2"])
        #expect(newsItem.isBookmarked == true)
    }

    @Test func testNewsItemEquality() async throws {
        let testDate = Date()

        let newsItem1 = NewsItem(
            id: "same_id",
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: testDate,
            category: .technology
        )

        let newsItem2 = NewsItem(
            id: "same_id",
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: testDate,
            category: .technology
        )

        let newsItem3 = NewsItem(
            id: "different_id",
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: testDate,
            category: .technology
        )

        #expect(newsItem1 == newsItem2, "NewsItems with same properties should be equal")
        #expect(newsItem1 != newsItem3, "NewsItems with different IDs should not be equal")
    }

    @Test func testNewsItemCodable() async throws {
        let originalNewsItem = NewsItem(
            id: "test_id",
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            imageURL: "https://example.com/image.jpg",
            author: "Test Author",
            category: .technology,
            tags: ["tag1", "tag2"],
            isBookmarked: true
        )

        // Test encoding
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalNewsItem)
        #expect(data.count > 0, "NewsItem should be encodable")

        // Test decoding
        let decoder = JSONDecoder()
        let decodedNewsItem = try decoder.decode(NewsItem.self, from: data)

        #expect(decodedNewsItem.id == originalNewsItem.id)
        #expect(decodedNewsItem.title == originalNewsItem.title)
        #expect(decodedNewsItem.description == originalNewsItem.description)
        #expect(decodedNewsItem.content == originalNewsItem.content)
        #expect(decodedNewsItem.imageURL == originalNewsItem.imageURL)
        #expect(decodedNewsItem.author == originalNewsItem.author)
        #expect(decodedNewsItem.category == originalNewsItem.category)
        #expect(decodedNewsItem.tags == originalNewsItem.tags)
        #expect(decodedNewsItem.isBookmarked == originalNewsItem.isBookmarked)
    }

    @Test func testNewsItemMockData() async throws {
        let mockItems = NewsItem.mockItems

        #expect(mockItems.count == 3, "Should have 3 mock news items")

        let firstItem = mockItems[0]
        #expect(firstItem.title == "Analytics that feels like it's from the future")
        #expect(firstItem.author == "John Smith")
        #expect(firstItem.category == .technology)
        #expect(firstItem.tags == ["analytics", "future", "technology"])

        let secondItem = mockItems[1]
        #expect(secondItem.title == "Portfolio performance tracking made easy")
        #expect(secondItem.author == "Jane Doe")
        #expect(secondItem.category == .finance)
        #expect(secondItem.tags == ["portfolio", "tracking", "finance"])

        let thirdItem = mockItems[2]
        #expect(thirdItem.title == "Start your free trial today")
        #expect(thirdItem.author == "Mike Johnson")
        #expect(thirdItem.category == .business)
        #expect(thirdItem.tags == ["trial", "business", "growth"])
    }

    @Test func testNewsItemFormattedPublishedDate() async throws {
        let testDate = Date()
        let newsItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: testDate,
            category: .technology
        )

        let formattedDate = newsItem.formattedPublishedDate
        #expect(!formattedDate.isEmpty, "Formatted date should not be empty")
        #expect(formattedDate.contains("2025"), "Formatted date should contain current year")
    }

    @Test func testNewsItemTimeAgo() async throws {
        let now = Date()

        // Test "Just now" (less than 60 seconds)
        let justNowItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: now.addingTimeInterval(-30), // 30 seconds ago
            category: .technology
        )
        #expect(justNowItem.timeAgo == "Just now")

        // Test minutes ago
        let minutesAgoItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: now.addingTimeInterval(-300), // 5 minutes ago
            category: .technology
        )
        #expect(minutesAgoItem.timeAgo == "5 minutes ago")

        // Test hours ago
        let hoursAgoItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: now.addingTimeInterval(-7200), // 2 hours ago
            category: .technology
        )
        #expect(hoursAgoItem.timeAgo == "2 hours ago")

        // Test days ago
        let daysAgoItem = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            publishedAt: now.addingTimeInterval(-172800), // 2 days ago
            category: .technology
        )
        #expect(daysAgoItem.timeAgo == "2 days ago")
    }

    @Test func testNewsItemEdgeCases() async throws {
        // Test with very long title
        let longTitle = String(repeating: "a", count: 1000)
        let newsItemLongTitle = NewsItem(
            title: longTitle,
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            category: .technology
        )
        #expect(newsItemLongTitle.title == longTitle)

        // Test with special characters
        let specialTitle = "Breaking: iOS 18.5 Released! 🎉 New Features & Updates"
        let newsItemSpecialTitle = NewsItem(
            title: specialTitle,
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            category: .technology
        )
        #expect(newsItemSpecialTitle.title == specialTitle)

        // Test with empty tags array
        let newsItemEmptyTags = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            category: .technology,
            tags: []
        )
        #expect(newsItemEmptyTags.tags.isEmpty)

        // Test with many tags
        let manyTags = Array(1...50).map { "tag\($0)" }
        let newsItemManyTags = NewsItem(
            title: "Test News",
            description: "Test Description",
            content: "Test Content",
            author: "Test Author",
            category: .technology,
            tags: manyTags
        )
        #expect(newsItemManyTags.tags.count == 50)
    }
}

// MARK: - NewsCategory Tests
struct NewsCategoryTests {

    @Test func testNewsCategoryDisplayNames() async throws {
        #expect(NewsCategory.technology.displayName == "Technology")
        #expect(NewsCategory.business.displayName == "Business")
        #expect(NewsCategory.finance.displayName == "Finance")
        #expect(NewsCategory.health.displayName == "Health")
        #expect(NewsCategory.sports.displayName == "Sports")
        #expect(NewsCategory.entertainment.displayName == "Entertainment")
        #expect(NewsCategory.general.displayName == "General")
    }

    @Test func testNewsCategoryColors() async throws {
        #expect(NewsCategory.technology.color == "7E56D8")
        #expect(NewsCategory.business.color == "52379E")
        #expect(NewsCategory.finance.color == "6840C6")
        #expect(NewsCategory.health.color == "422F7D")
        #expect(NewsCategory.sports.color == "FF5252")
        #expect(NewsCategory.entertainment.color == "667085")
        #expect(NewsCategory.general.color == "101828")
    }

    @Test func testNewsCategoryRawValues() async throws {
        #expect(NewsCategory.technology.rawValue == "technology")
        #expect(NewsCategory.business.rawValue == "business")
        #expect(NewsCategory.finance.rawValue == "finance")
        #expect(NewsCategory.health.rawValue == "health")
        #expect(NewsCategory.sports.rawValue == "sports")
        #expect(NewsCategory.entertainment.rawValue == "entertainment")
        #expect(NewsCategory.general.rawValue == "general")
    }

    @Test func testNewsCategoryCaseIterable() async throws {
        let allCases = NewsCategory.allCases
        #expect(allCases.count == 7)
        #expect(allCases.contains(.technology))
        #expect(allCases.contains(.business))
        #expect(allCases.contains(.finance))
        #expect(allCases.contains(.health))
        #expect(allCases.contains(.sports))
        #expect(allCases.contains(.entertainment))
        #expect(allCases.contains(.general))
    }

    @Test func testNewsCategoryCodable() async throws {
        let category = NewsCategory.technology

        // Test encoding
        let encoder = JSONEncoder()
        let data = try encoder.encode(category)
        #expect(data.count > 0, "NewsCategory should be encodable")

        // Test decoding
        let decoder = JSONDecoder()
        let decodedCategory = try decoder.decode(NewsCategory.self, from: data)
        #expect(decodedCategory == category)
    }
}

// MARK: - Home Models Tests

// MARK: - FeatureItem Tests
struct FeatureItemTests {

    @Test func testFeatureItemInitialization() async throws {
        let featureItem = FeatureItem(
            icon: "chart.pie",
            title: "Test Feature",
            description: "Test Description"
        )

        #expect(!featureItem.id.uuidString.isEmpty, "FeatureItem ID should be generated")
        #expect(featureItem.icon == "chart.pie")
        #expect(featureItem.title == "Test Feature")
        #expect(featureItem.description == "Test Description")
    }

    @Test func testFeatureItemEquality() async throws {
        let featureItem1 = FeatureItem(
            icon: "chart.pie",
            title: "Test Feature",
            description: "Test Description"
        )

        let featureItem2 = FeatureItem(
            icon: "chart.pie",
            title: "Test Feature",
            description: "Test Description"
        )

        let featureItem3 = FeatureItem(
            icon: "bolt",
            title: "Different Feature",
            description: "Different Description"
        )

        // Note: FeatureItems with same content but different UUIDs will not be equal
        #expect(featureItem1 != featureItem2, "FeatureItems with different UUIDs should not be equal")
        #expect(featureItem1 != featureItem3, "FeatureItems with different content should not be equal")

        // Test equality with same instance
        #expect(featureItem1 == featureItem1, "FeatureItem should be equal to itself")
    }

    @Test func testFeatureItemMockData() async throws {
        let mockFeatures = FeatureItem.mockFeatures

        #expect(mockFeatures.count == 4, "Should have 4 mock features")

        let firstFeature = mockFeatures[0]
        #expect(firstFeature.icon == "chart.pie")
        #expect(firstFeature.title == "Access to daily analytics")
        #expect(firstFeature.description.contains("Optimize the way you recover"))

        let secondFeature = mockFeatures[1]
        #expect(secondFeature.icon == "bolt")
        #expect(secondFeature.title == "Measure recovery")
        #expect(secondFeature.description.contains("sleep tracking technology"))

        let thirdFeature = mockFeatures[2]
        #expect(thirdFeature.icon == "iphone")
        #expect(thirdFeature.title == "Tech that evolves with you")
        #expect(thirdFeature.description.contains("strengths lie"))

        let fourthFeature = mockFeatures[3]
        #expect(fourthFeature.icon == "person.3")
        #expect(fourthFeature.title == "Unrivalled community")
        #expect(fourthFeature.description.contains("Join teams"))
    }

    @Test func testFeatureItemEdgeCases() async throws {
        // Test with empty strings
        let emptyFeature = FeatureItem(
            icon: "",
            title: "",
            description: ""
        )
        #expect(emptyFeature.icon == "")
        #expect(emptyFeature.title == "")
        #expect(emptyFeature.description == "")

        // Test with very long strings
        let longTitle = String(repeating: "a", count: 1000)
        let longDescription = String(repeating: "b", count: 2000)
        let longFeature = FeatureItem(
            icon: "test.icon",
            title: longTitle,
            description: longDescription
        )
        #expect(longFeature.title == longTitle)
        #expect(longFeature.description == longDescription)

        // Test with special characters
        let specialFeature = FeatureItem(
            icon: "🎯",
            title: "Feature with émojis & spëcial chars",
            description: "Description with 中文 and العربية"
        )
        #expect(specialFeature.icon == "🎯")
        #expect(specialFeature.title == "Feature with émojis & spëcial chars")
        #expect(specialFeature.description == "Description with 中文 and العربية")
    }
}

// MARK: - PricingPlan Tests
struct PricingPlanTests {

    @Test func testPricingPlanInitialization() async throws {
        let pricingPlan = PricingPlan(
            price: "$10/mth",
            title: "Basic Plan",
            description: "Billed annually",
            isPopular: true,
            features: ["Feature 1", "Feature 2"]
        )

        #expect(!pricingPlan.id.uuidString.isEmpty, "PricingPlan ID should be generated")
        #expect(pricingPlan.price == "$10/mth")
        #expect(pricingPlan.title == "Basic Plan")
        #expect(pricingPlan.description == "Billed annually")
        #expect(pricingPlan.isPopular == true)
        #expect(pricingPlan.features == ["Feature 1", "Feature 2"])
    }

    @Test func testPricingPlanEquality() async throws {
        let plan1 = PricingPlan(
            price: "$10/mth",
            title: "Basic Plan",
            description: "Billed annually",
            isPopular: true,
            features: ["Feature 1", "Feature 2"]
        )

        let plan2 = PricingPlan(
            price: "$10/mth",
            title: "Basic Plan",
            description: "Billed annually",
            isPopular: true,
            features: ["Feature 1", "Feature 2"]
        )

        let plan3 = PricingPlan(
            price: "$20/mth",
            title: "Premium Plan",
            description: "Billed annually",
            isPopular: false,
            features: ["Feature 1", "Feature 2", "Feature 3"]
        )

        // Note: PricingPlans with same content but different UUIDs will not be equal
        #expect(plan1 != plan2, "PricingPlans with different UUIDs should not be equal")
        #expect(plan1 != plan3, "PricingPlans with different content should not be equal")

        // Test equality with same instance
        #expect(plan1 == plan1, "PricingPlan should be equal to itself")
    }

    @Test func testPricingPlanMockData() async throws {
        let mockPlans = PricingPlan.mockPlans

        #expect(mockPlans.count == 2, "Should have 2 mock pricing plans")

        let basicPlan = mockPlans[0]
        #expect(basicPlan.price == "$10/mth")
        #expect(basicPlan.title == "Basic plan")
        #expect(basicPlan.description == "Billed annually.")
        #expect(basicPlan.isPopular == true)
        #expect(basicPlan.features.count == 5)
        #expect(basicPlan.features.contains("Access to all basic features"))
        #expect(basicPlan.features.contains("Basic reporting and analytics"))

        let businessPlan = mockPlans[1]
        #expect(businessPlan.price == "$20/mth")
        #expect(businessPlan.title == "Business plan")
        #expect(businessPlan.description == "Billed annually.")
        #expect(businessPlan.isPopular == false)
        #expect(businessPlan.features.count == 5)
        #expect(businessPlan.features.contains("200+ integrations"))
        #expect(businessPlan.features.contains("Advanced reporting"))
    }

    @Test func testPricingPlanEdgeCases() async throws {
        // Test with empty features array
        let emptyFeaturesPlan = PricingPlan(
            price: "$0/mth",
            title: "Free Plan",
            description: "No cost",
            isPopular: false,
            features: []
        )
        #expect(emptyFeaturesPlan.features.isEmpty)

        // Test with many features
        let manyFeatures = Array(1...100).map { "Feature \($0)" }
        let manyFeaturesPlan = PricingPlan(
            price: "$100/mth",
            title: "Enterprise Plan",
            description: "All features included",
            isPopular: false,
            features: manyFeatures
        )
        #expect(manyFeaturesPlan.features.count == 100)

        // Test with special characters in price
        let specialPricePlan = PricingPlan(
            price: "€15,99/mês",
            title: "European Plan",
            description: "Billed in Euros",
            isPopular: true,
            features: ["Feature with special chars: àáâãäå"]
        )
        #expect(specialPricePlan.price == "€15,99/mês")
        #expect(specialPricePlan.features[0] == "Feature with special chars: àáâãäå")
    }
}

// MARK: - PartnerLogo Tests
struct PartnerLogoTests {

    @Test func testPartnerLogoInitialization() async throws {
        let partnerLogo = PartnerLogo(
            logoName: "apple.logo",
            companyName: "Apple"
        )

        #expect(!partnerLogo.id.uuidString.isEmpty, "PartnerLogo ID should be generated")
        #expect(partnerLogo.logoName == "apple.logo")
        #expect(partnerLogo.companyName == "Apple")
    }

    @Test func testPartnerLogoEquality() async throws {
        let logo1 = PartnerLogo(logoName: "apple.logo", companyName: "Apple")
        let logo2 = PartnerLogo(logoName: "apple.logo", companyName: "Apple")
        let logo3 = PartnerLogo(logoName: "google.logo", companyName: "Google")

        // Note: PartnerLogos with same content but different UUIDs will not be equal
        #expect(logo1 != logo2, "PartnerLogos with different UUIDs should not be equal")
        #expect(logo1 != logo3, "PartnerLogos with different content should not be equal")

        // Test equality with same instance
        #expect(logo1 == logo1, "PartnerLogo should be equal to itself")
    }

    @Test func testPartnerLogoEdgeCases() async throws {
        // Test with empty strings
        let emptyLogo = PartnerLogo(logoName: "", companyName: "")
        #expect(emptyLogo.logoName == "")
        #expect(emptyLogo.companyName == "")

        // Test with special characters
        let specialLogo = PartnerLogo(
            logoName: "company-logo_2024.svg",
            companyName: "Compañía Española & Co."
        )
        #expect(specialLogo.logoName == "company-logo_2024.svg")
        #expect(specialLogo.companyName == "Compañía Española & Co.")

        // Test with very long names
        let longName = String(repeating: "Company", count: 100)
        let longLogo = PartnerLogo(logoName: "logo.png", companyName: longName)
        #expect(longLogo.companyName == longName)
    }
}

// MARK: - LandingData Tests
struct LandingDataTests {

    @Test func testLandingDataInitialization() async throws {
        let features = [
            FeatureItem(icon: "icon1", title: "Feature 1", description: "Description 1"),
            FeatureItem(icon: "icon2", title: "Feature 2", description: "Description 2")
        ]

        let pricingPlans = [
            PricingPlan(price: "$10", title: "Basic", description: "Basic plan", isPopular: true, features: ["Feature 1"]),
            PricingPlan(price: "$20", title: "Pro", description: "Pro plan", isPopular: false, features: ["Feature 1", "Feature 2"])
        ]

        let partnerLogos = [
            PartnerLogo(logoName: "logo1", companyName: "Company 1"),
            PartnerLogo(logoName: "logo2", companyName: "Company 2")
        ]

        let landingData = LandingData(
            features: features,
            pricingPlans: pricingPlans,
            partnerLogos: partnerLogos
        )

        #expect(landingData.features.count == 2)
        #expect(landingData.pricingPlans.count == 2)
        #expect(landingData.partnerLogos.count == 2)
        #expect(landingData.features[0].title == "Feature 1")
        #expect(landingData.pricingPlans[0].title == "Basic")
        #expect(landingData.partnerLogos[0].companyName == "Company 1")
    }

    @Test func testLandingDataDefaultData() async throws {
        let defaultData = LandingData.default

        // Test features
        #expect(defaultData.features.count == 4, "Default data should have 4 features")
        #expect(defaultData.features[0].icon == "chart.pie")
        #expect(defaultData.features[0].title == "Access to daily analytics")
        #expect(defaultData.features[1].icon == "bolt")
        #expect(defaultData.features[1].title == "Measure recovery")
        #expect(defaultData.features[2].icon == "iphone")
        #expect(defaultData.features[2].title == "Tech that evolves with you")
        #expect(defaultData.features[3].icon == "person.3")
        #expect(defaultData.features[3].title == "Unrivalled community")

        // Test pricing plans
        #expect(defaultData.pricingPlans.count == 2, "Default data should have 2 pricing plans")

        let basicPlan = defaultData.pricingPlans[0]
        #expect(basicPlan.price == "$10/mth")
        #expect(basicPlan.title == "Basic plan")
        #expect(basicPlan.isPopular == true)
        #expect(basicPlan.features.count == 5)

        let businessPlan = defaultData.pricingPlans[1]
        #expect(businessPlan.price == "$20/mth")
        #expect(businessPlan.title == "Business plan")
        #expect(businessPlan.isPopular == false)
        #expect(businessPlan.features.count == 5)

        // Test partner logos
        #expect(defaultData.partnerLogos.count == 6, "Default data should have 6 partner logos")

        let expectedCompanies = ["Apple", "Microsoft", "Google", "Amazon", "Meta", "Netflix"]
        let actualCompanies = defaultData.partnerLogos.map { $0.companyName }

        for expectedCompany in expectedCompanies {
            #expect(actualCompanies.contains(expectedCompany), "Should contain \(expectedCompany)")
        }

        // Test specific partner logos
        let appleLogo = defaultData.partnerLogos.first { $0.companyName == "Apple" }
        #expect(appleLogo?.logoName == "apple.logo")

        let googleLogo = defaultData.partnerLogos.first { $0.companyName == "Google" }
        #expect(googleLogo?.logoName == "google.logo")
    }

    @Test func testLandingDataEdgeCases() async throws {
        // Test with empty arrays
        let emptyData = LandingData(
            features: [],
            pricingPlans: [],
            partnerLogos: []
        )
        #expect(emptyData.features.isEmpty)
        #expect(emptyData.pricingPlans.isEmpty)
        #expect(emptyData.partnerLogos.isEmpty)

        // Test with single items
        let singleItemData = LandingData(
            features: [FeatureItem(icon: "icon", title: "Title", description: "Description")],
            pricingPlans: [PricingPlan(price: "$5", title: "Plan", description: "Desc", isPopular: false, features: [])],
            partnerLogos: [PartnerLogo(logoName: "logo", companyName: "Company")]
        )
        #expect(singleItemData.features.count == 1)
        #expect(singleItemData.pricingPlans.count == 1)
        #expect(singleItemData.partnerLogos.count == 1)

        // Test with large arrays
        let manyFeatures = Array(1...50).map { FeatureItem(icon: "icon\($0)", title: "Title \($0)", description: "Description \($0)") }
        let manyPlans = Array(1...20).map { PricingPlan(price: "$\($0)", title: "Plan \($0)", description: "Desc \($0)", isPopular: $0 % 2 == 0, features: ["Feature \($0)"]) }
        let manyLogos = Array(1...30).map { PartnerLogo(logoName: "logo\($0)", companyName: "Company \($0)") }

        let largeData = LandingData(
            features: manyFeatures,
            pricingPlans: manyPlans,
            partnerLogos: manyLogos
        )
        #expect(largeData.features.count == 50)
        #expect(largeData.pricingPlans.count == 20)
        #expect(largeData.partnerLogos.count == 30)
    }
}
