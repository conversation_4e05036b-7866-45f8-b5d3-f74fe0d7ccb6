import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../domain/usecases/landing/get_features_usecase.dart';
import '../../../domain/usecases/landing/get_pricing_plans_usecase.dart';
import '../models/feature_item_model.dart';
import '../models/landing_model.dart';
import '../models/pricing_plan_model.dart';

part 'landing_event.dart';
part 'landing_state.dart';

class LandingBloc extends Bloc<LandingEvent, LandingState> {
  final GetFeaturesUseCase _getFeaturesUseCase;
  final GetPricingPlansUseCase _getPricingPlansUseCase;

  LandingBloc(super.initialState, {required GetFeaturesUseCase getFeaturesUseCase, required GetPricingPlansUseCase getPricingPlansUseCase})
    : _getFeaturesUseCase = getFeaturesUseCase,
      _getPricingPlansUseCase = getPricingPlansUseCase {
    on<LandingInitialEvent>(_onInitialize);
    on<MenuButtonPressedEvent>(_onMenuButtonPressed);
    on<AppStoreButtonPressedEvent>(_onAppStoreButtonPressed);
    on<PlayStoreButtonPressedEvent>(_onPlayStoreButtonPressed);
    on<CtaAppStoreButtonPressedEvent>(_onCtaAppStoreButtonPressed);
    on<CtaPlayStoreButtonPressedEvent>(_onCtaPlayStoreButtonPressed);
    on<PricingPlanSelectedEvent>(_onPricingPlanSelected);
    on<EmailChangedEvent>(_onEmailChanged);
    on<SubscribeButtonPressedEvent>(_onSubscribeButtonPressed);
    on<BottomNavItemTappedEvent>(_onBottomNavItemTapped);
  }

  _onInitialize(LandingInitialEvent event, Emitter<LandingState> emit) async {
    // Load features and pricing plans using use cases
    final featuresResult = await _getFeaturesUseCase();
    final pricingPlansResult = await _getPricingPlansUseCase();

    // Handle results and convert to presentation models
    featuresResult.fold(
      (failure) {
        // On failure, emit empty state with email controller
        emit(
          state.copyWith(
            emailController: TextEditingController(),
            landingModel: state.landingModel?.copyWith(features: <FeatureItemModel>[], pricingPlans: <PricingPlanModel>[]),
          ),
        );
      },
      (features) {
        pricingPlansResult.fold(
          (failure) {
            // On failure, emit empty state with email controller
            emit(
              state.copyWith(
                emailController: TextEditingController(),
                landingModel: state.landingModel?.copyWith(features: <FeatureItemModel>[], pricingPlans: <PricingPlanModel>[]),
              ),
            );
          },
          (pricingPlans) {
            // Convert domain entities to presentation models
            final featureModels = features
                .map((feature) => FeatureItemModel(title: feature.title, description: feature.description, icon: feature.iconPath))
                .toList();

            final pricingPlanModels = pricingPlans
                .map(
                  (plan) => PricingPlanModel(
                    price: plan.price,
                    title: plan.title,
                    description: plan.description,
                    isPopular: plan.isPopular,
                    features: plan.features,
                  ),
                )
                .toList();

            emit(
              state.copyWith(
                emailController: TextEditingController(),
                landingModel: state.landingModel?.copyWith(features: featureModels, pricingPlans: pricingPlanModels),
              ),
            );
          },
        );
      },
    );
  }

  _onMenuButtonPressed(MenuButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle menu button press
  }

  _onAppStoreButtonPressed(AppStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle App Store button press
  }

  _onPlayStoreButtonPressed(PlayStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle Play Store button press
  }

  _onCtaAppStoreButtonPressed(CtaAppStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle CTA App Store button press
  }

  _onCtaPlayStoreButtonPressed(CtaPlayStoreButtonPressedEvent event, Emitter<LandingState> emit) async {
    // Handle CTA Play Store button press
  }

  _onPricingPlanSelected(PricingPlanSelectedEvent event, Emitter<LandingState> emit) async {
    emit(state.copyWith(selectedPlan: event.plan));
  }

  _onEmailChanged(EmailChangedEvent event, Emitter<LandingState> emit) async {
    emit(state.copyWith(email: event.email));
  }

  _onSubscribeButtonPressed(SubscribeButtonPressedEvent event, Emitter<LandingState> emit) async {
    if (state.email?.isNotEmpty ?? false) {
      emit(state.copyWith(isSubscribed: true));
      state.emailController?.clear();
    }
  }

  _onBottomNavItemTapped(BottomNavItemTappedEvent event, Emitter<LandingState> emit) async {
    emit(state.copyWith(selectedNavItem: event.item));
  }

  @override
  Future<void> close() {
    // Dispose TextEditingController to prevent memory leaks
    state.emailController?.dispose();
    return super.close();
  }
}
